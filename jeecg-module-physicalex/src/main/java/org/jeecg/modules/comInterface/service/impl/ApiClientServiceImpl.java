package org.jeecg.modules.comInterface.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.comInterface.entity.ApiClient;
import org.jeecg.modules.comInterface.mapper.ApiClientMapper;
import org.jeecg.modules.comInterface.service.IApiClientService;
import org.jeecg.modules.comInterface.util.ApiKeyGenerator;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.UUID;

/**
 * @Description: API客户端配置Service实现
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Service
public class ApiClientServiceImpl extends ServiceImpl<ApiClientMapper, ApiClient> implements IApiClientService {

    @Override
    public ApiClient getByApiKey(String apiKey) {
        LambdaQueryWrapper<ApiClient> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApiClient::getApiKey, apiKey);
        queryWrapper.eq(ApiClient::getDelFlag, 0);
        return this.getOne(queryWrapper);
    }

    @Override
    public ApiClient generateApiKeys() {
        ApiClient apiClient = new ApiClient();

        // 生成API密钥（32位UUID去掉横线）
        String apiKey = UUID.randomUUID().toString().replace("-", "");
        apiClient.setApiKey(apiKey);

        // 生成API秘钥（64位UUID去掉横线）
        String apiSecret = UUID.randomUUID().toString().replace("-", "") +
                          UUID.randomUUID().toString().replace("-", "");
        apiClient.setApiSecret(apiSecret);

        // 默认启用状态
        apiClient.setStatus(1);
        apiClient.setDelFlag(0);

        return apiClient;
    }

    @Override
    public ApiClient generateSecureApiKeys() {
        ApiClient apiClient = new ApiClient();

        // 使用ApiKeyGenerator生成高强度密钥
        ApiKeyGenerator.KeyPair keyPair = ApiKeyGenerator.generateSecureKeyPair();

        apiClient.setApiKey(keyPair.getApiKey());
        apiClient.setApiSecret(keyPair.getApiSecret());

        // 默认启用状态
        apiClient.setStatus(1);
        apiClient.setDelFlag(0);

        return apiClient;
    }

    @Override
    public int validateKeyStrength(String apiKey, String apiSecret) {
        if (apiKey == null || apiSecret == null) {
            return 1; // 弱
        }

        // 使用ApiKeyGenerator计算强度
        int keyStrength = ApiKeyGenerator.calculateKeyStrength(apiKey);
        int secretStrength = ApiKeyGenerator.calculateKeyStrength(apiSecret);

        // 计算平均强度并转换为1-3等级
        int avgStrength = (keyStrength + secretStrength) / 2;

        if (avgStrength >= 7) return 3; // 强
        if (avgStrength >= 4) return 2; // 中
        return 1; // 弱
    }


}
