<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="ApiClientForm">
          <a-row>
						<a-col :span="24">
							<a-form-item label="客户端名称" v-bind="validateInfos.clientName" id="ApiClientForm-clientName" name="clientName">
								<a-input v-model:value="formData.clientName" placeholder="请输入客户端名称"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="API密钥" v-bind="validateInfos.apiKey" id="ApiClientForm-apiKey" name="apiKey">
								<a-input-group compact>
									<a-input v-model:value="formData.apiKey" placeholder="请输入API密钥" allow-clear style="width: calc(100% - 200px)" />
									<a-button type="primary" @click="handleGenerateKeys" :loading="generateLoading" style="width: 100px">
										一键生成
									</a-button>
									<a-dropdown>
										<a-button style="width: 100px">
											更多选项 <DownOutlined />
										</a-button>
										<template #overlay>
											<a-menu @click="handleGenerateByType">
												<a-menu-item key="STANDARD">标准密钥</a-menu-item>
												<a-menu-item key="SECURE">高强度密钥</a-menu-item>
												<a-menu-item key="HEX">十六进制密钥</a-menu-item>
												<a-menu-item key="BASE64">Base64密钥</a-menu-item>
												<a-menu-item key="CUSTOM">自定义密钥</a-menu-item>
											</a-menu>
										</template>
									</a-dropdown>
								</a-input-group>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="API秘钥" v-bind="validateInfos.apiSecret" id="ApiClientForm-apiSecret" name="apiSecret">
								<a-input-group compact>
									<a-input v-model:value="formData.apiSecret" placeholder="请输入API秘钥" allow-clear style="width: calc(100% - 100px)" />
									<a-button @click="handleValidateStrength" :loading="validateLoading" style="width: 100px">
										验证强度
									</a-button>
								</a-input-group>
								<div v-if="keyStrength > 0" class="key-strength-info" style="margin-top: 8px;">
									<a-tag :color="getStrengthColor(keyStrength)">
										密钥强度: {{ getStrengthText(keyStrength) }}
									</a-tag>
								</div>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="状态：1-启用，0-禁用" v-bind="validateInfos.status" id="ApiClientForm-status" name="status">
								<j-switch v-model:value="formData.status" :options="[1,0]" ></j-switch>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="过期时间" v-bind="validateInfos.expireTime" id="ApiClientForm-expireTime" name="expireTime">
								<a-date-picker placeholder="请选择过期时间"  v-model:value="formData.expireTime" showTime value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="IP白名单，多个IP用逗号分隔" v-bind="validateInfos.ipWhitelist" id="ApiClientForm-ipWhitelist" name="ipWhitelist">
								<a-textarea v-model:value="formData.ipWhitelist" :rows="4" placeholder="请输入IP白名单，多个IP用逗号分隔" />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="备注" v-bind="validateInfos.remark" id="ApiClientForm-remark" name="remark">
								<a-textarea v-model:value="formData.remark" :rows="4" placeholder="请输入备注" />
							</a-form-item>
						</a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>

    <!-- 自定义密钥生成器 -->
    <a-modal
      title="自定义密钥生成器"
      :visible="customModalVisible"
      :width="500"
      @ok="handleCustomGenerate"
      @cancel="customModalVisible = false"
      :confirmLoading="generateLoading"
    >
      <a-form :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
        <a-form-item label="API密钥长度">
          <a-input-number v-model:value="customKeyLength" :min="16" :max="128" style="width: 100%" />
        </a-form-item>
        <a-form-item label="API秘钥长度">
          <a-input-number v-model:value="customSecretLength" :min="32" :max="256" style="width: 100%" />
        </a-form-item>
        <a-form-item label="包含特殊字符">
          <a-switch v-model:checked="includeSpecialChars" />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate, generateKeys, generateKeysByType, generateCustomKeys, validateKeyStrength } from '../ApiClient.api';
  import { Form } from 'ant-design-vue';
  import { DownOutlined } from '@ant-design/icons-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  // import CustomKeyGeneratorModal from './CustomKeyGeneratorModal.vue';
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({})},
    formBpm: { type: Boolean, default: true }
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    clientName: '',   
    apiKey: '',   
    apiSecret: '',   
    status: undefined,
    expireTime: '',   
    ipWhitelist: '',   
    remark: '',   
    delFlag: undefined,
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  const generateLoading = ref<boolean>(false);
  const validateLoading = ref<boolean>(false);
  const keyStrength = ref<number>(0);
  const customModalVisible = ref<boolean>(false);
  const customKeyLength = ref<number>(32);
  const customSecretLength = ref<number>(64);
  const includeSpecialChars = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    clientName: [{ required: true, message: '请输入客户端名称!'},],
    apiKey: [{ required: true, message: '请输入API密钥!'},],
    apiSecret: [{ required: true, message: '请输入API秘钥!'},],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(()=>{
    if(props.formBpm === true){
      if(props.formData.disabled === false){
        return false;
      }else{
        return true;
      }
    }
    return props.formDisabled;
  });

  
  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if(record.hasOwnProperty(key)){
          tmpData[key] = record[key]
        }
      })
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 一键生成API密钥
   */
  async function handleGenerateKeys() {
    generateLoading.value = true;
    try {
      const res = await generateKeys();
      if (res.success) {
        formData.apiKey = res.result.apiKey;
        formData.apiSecret = res.result.apiSecret;
        createMessage.success('API密钥生成成功！');
        // 自动验证强度
        await handleValidateStrength();
      } else {
        createMessage.error(res.message || '生成失败');
      }
    } catch (error) {
      createMessage.error('生成API密钥失败');
    } finally {
      generateLoading.value = false;
    }
  }

  /**
   * 根据类型生成密钥
   */
  async function handleGenerateByType({ key }) {
    if (key === 'CUSTOM') {
      // 显示自定义密钥对话框
      showCustomKeyDialog();
      return;
    }

    generateLoading.value = true;
    try {
      const res = await generateKeysByType(key);
      if (res.success) {
        formData.apiKey = res.result.apiKey;
        formData.apiSecret = res.result.apiSecret;
        createMessage.success(`${getKeyTypeText(key)}生成成功！`);
        // 自动验证强度
        await handleValidateStrength();
      } else {
        createMessage.error(res.message || '生成失败');
      }
    } catch (error) {
      createMessage.error('生成API密钥失败');
    } finally {
      generateLoading.value = false;
    }
  }

  /**
   * 验证密钥强度
   */
  async function handleValidateStrength() {
    if (!formData.apiKey || !formData.apiSecret) {
      createMessage.warning('请先输入API密钥和秘钥');
      return;
    }

    validateLoading.value = true;
    try {
      const res = await validateKeyStrength(formData.apiKey, formData.apiSecret);
      if (res.success) {
        keyStrength.value = res.result;
        createMessage.success(`密钥强度验证完成: ${getStrengthText(res.result)}`);
      } else {
        createMessage.error(res.message || '验证失败');
      }
    } catch (error) {
      createMessage.error('验证密钥强度失败');
    } finally {
      validateLoading.value = false;
    }
  }

  /**
   * 显示自定义密钥对话框
   */
  function showCustomKeyDialog() {
    customModalVisible.value = true;
  }

  /**
   * 处理自定义密钥生成
   */
  async function handleCustomGenerate() {
    generateLoading.value = true;
    try {
      const res = await generateCustomKeys(
        customKeyLength.value,
        customSecretLength.value,
        includeSpecialChars.value
      );
      if (res.success) {
        formData.apiKey = res.result.apiKey;
        formData.apiSecret = res.result.apiSecret;
        customModalVisible.value = false;
        createMessage.success('自定义密钥生成成功！');
        // 自动验证强度
        await handleValidateStrength();
      } else {
        createMessage.error(res.message || '生成失败');
      }
    } catch (error) {
      createMessage.error('生成自定义密钥失败');
    } finally {
      generateLoading.value = false;
    }
  }

  /**
   * 处理自定义密钥生成完成
   */
  function handleCustomKeyGenerated(keyData) {
    formData.apiKey = keyData.apiKey;
    formData.apiSecret = keyData.apiSecret;
    keyStrength.value = keyData.strength;
    createMessage.success('自定义密钥应用成功！');
  }

  /**
   * 获取密钥类型文本
   */
  function getKeyTypeText(type) {
    const typeMap = {
      'STANDARD': '标准密钥',
      'SECURE': '高强度密钥',
      'HEX': '十六进制密钥',
      'BASE64': 'Base64密钥'
    };
    return typeMap[type] || '密钥';
  }

  /**
   * 获取强度文本
   */
  function getStrengthText(strength) {
    const strengthMap = {
      1: '弱',
      2: '中',
      3: '强'
    };
    return strengthMap[strength] || '未知';
  }

  /**
   * 获取强度颜色
   */
  function getStrengthColor(strength) {
    const colorMap = {
      1: 'red',
      2: 'orange',
      3: 'green'
    };
    return colorMap[strength] || 'default';
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }


  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
