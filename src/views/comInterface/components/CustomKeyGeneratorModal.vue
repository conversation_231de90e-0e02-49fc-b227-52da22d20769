<template>
  <a-modal
    title="自定义密钥生成器"
    :visible="visible"
    :width="600"
    @ok="handleGenerate"
    @cancel="handleCancel"
    :confirmLoading="loading"
  >
    <a-form :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
      <a-form-item label="API密钥长度">
        <a-slider
          v-model:value="keyLength"
          :min="16"
          :max="128"
          :marks="keyLengthMarks"
          :tooltip-formatter="(value) => `${value}位`"
        />
        <a-input-number
          v-model:value="keyLength"
          :min="16"
          :max="128"
          style="margin-top: 8px; width: 100px"
        />
      </a-form-item>

      <a-form-item label="API秘钥长度">
        <a-slider
          v-model:value="secretLength"
          :min="32"
          :max="256"
          :marks="secretLengthMarks"
          :tooltip-formatter="(value) => `${value}位`"
        />
        <a-input-number
          v-model:value="secretLength"
          :min="32"
          :max="256"
          style="margin-top: 8px; width: 100px"
        />
      </a-form-item>

      <a-form-item label="字符类型">
        <a-checkbox-group v-model:value="charTypes">
          <a-checkbox value="uppercase">大写字母 (A-Z)</a-checkbox>
          <a-checkbox value="lowercase">小写字母 (a-z)</a-checkbox>
          <a-checkbox value="numbers">数字 (0-9)</a-checkbox>
          <a-checkbox value="special">特殊字符 (!@#$%^&*)</a-checkbox>
        </a-checkbox-group>
      </a-form-item>

      <a-form-item label="预览">
        <div class="preview-container">
          <div class="preview-item">
            <label>API密钥预览:</label>
            <a-input
              :value="previewKey"
              readonly
              style="margin-top: 4px; font-family: monospace"
            />
          </div>
          <div class="preview-item" style="margin-top: 12px">
            <label>API秘钥预览:</label>
            <a-input
              :value="previewSecret"
              readonly
              style="margin-top: 4px; font-family: monospace"
            />
          </div>
          <div style="margin-top: 12px">
            <a-button @click="generatePreview" size="small">
              生成预览
            </a-button>
            <span style="margin-left: 12px">
              <a-tag v-if="previewStrength > 0" :color="getStrengthColor(previewStrength)">
                预估强度: {{ getStrengthText(previewStrength) }}
              </a-tag>
            </span>
          </div>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, watch, defineEmits, defineExpose } from 'vue';
  import { generateCustomKeys } from '../ApiClient.api';
  import { useMessage } from '/@/hooks/web/useMessage';

  const emit = defineEmits(['ok']);
  const { createMessage } = useMessage();

  const visible = ref(false);
  const loading = ref(false);
  const keyLength = ref(32);
  const secretLength = ref(64);
  const charTypes = ref(['uppercase', 'lowercase', 'numbers']);
  const previewKey = ref('');
  const previewSecret = ref('');
  const previewStrength = ref(0);

  const keyLengthMarks = {
    16: '16',
    32: '32',
    64: '64',
    128: '128'
  };

  const secretLengthMarks = {
    32: '32',
    64: '64',
    128: '128',
    256: '256'
  };

  /**
   * 显示模态框
   */
  function show() {
    visible.value = true;
    generatePreview();
  }

  /**
   * 隐藏模态框
   */
  function hide() {
    visible.value = false;
  }

  /**
   * 生成预览
   */
  async function generatePreview() {
    try {
      const includeSpecialChars = charTypes.value.includes('special');
      const res = await generateCustomKeys(keyLength.value, secretLength.value, includeSpecialChars);
      
      if (res.success) {
        previewKey.value = res.result.apiKey;
        previewSecret.value = res.result.apiSecret;
        // 简单的强度估算
        previewStrength.value = estimateStrength();
      }
    } catch (error) {
      console.error('生成预览失败:', error);
    }
  }

  /**
   * 估算强度
   */
  function estimateStrength() {
    let score = 0;
    
    // 长度分数
    if (keyLength.value >= 32 && secretLength.value >= 64) score += 2;
    else if (keyLength.value >= 16 && secretLength.value >= 32) score += 1;
    
    // 字符类型分数
    score += charTypes.value.length;
    
    if (score >= 6) return 3; // 强
    if (score >= 4) return 2; // 中
    return 1; // 弱
  }

  /**
   * 生成密钥
   */
  async function handleGenerate() {
    if (charTypes.value.length === 0) {
      createMessage.warning('请至少选择一种字符类型');
      return;
    }

    loading.value = true;
    try {
      const includeSpecialChars = charTypes.value.includes('special');
      const res = await generateCustomKeys(keyLength.value, secretLength.value, includeSpecialChars);
      
      if (res.success) {
        emit('ok', {
          apiKey: res.result.apiKey,
          apiSecret: res.result.apiSecret,
          strength: estimateStrength()
        });
        createMessage.success('自定义密钥生成成功！');
        hide();
      } else {
        createMessage.error(res.message || '生成失败');
      }
    } catch (error) {
      createMessage.error('生成自定义密钥失败');
    } finally {
      loading.value = false;
    }
  }

  /**
   * 取消
   */
  function handleCancel() {
    hide();
  }

  /**
   * 获取强度文本
   */
  function getStrengthText(strength) {
    const strengthMap = {
      1: '弱',
      2: '中',
      3: '强'
    };
    return strengthMap[strength] || '未知';
  }

  /**
   * 获取强度颜色
   */
  function getStrengthColor(strength) {
    const colorMap = {
      1: 'red',
      2: 'orange',
      3: 'green'
    };
    return colorMap[strength] || 'default';
  }

  // 监听参数变化，自动生成预览
  watch([keyLength, secretLength, charTypes], () => {
    if (visible.value) {
      generatePreview();
    }
  }, { deep: true });

  defineExpose({
    show,
    hide
  });
</script>

<style lang="less" scoped>
  .preview-container {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
  }

  .preview-item {
    label {
      font-weight: 500;
      color: #666;
    }
  }
</style>
