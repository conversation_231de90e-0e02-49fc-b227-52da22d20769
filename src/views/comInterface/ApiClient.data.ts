import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '客户端名称',
    align: "center",
    dataIndex: 'clientName'
  },
  {
    title: 'API密钥',
    align: "center",
    dataIndex: 'apiKey'
  },
  {
    title: '状态：1-启用，0-禁用',
    align: "center",
    dataIndex: 'status',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'1'},{text:'否',value:'0'}]);
     },
  },
  {
    title: '过期时间',
    align: "center",
    dataIndex: 'expireTime'
  },
  {
    title: 'IP白名单，多个IP用逗号分隔',
    align: "center",
    dataIndex: 'ipWhitelist'
  },
  {
    title: '备注',
    align: "center",
    dataIndex: 'remark'
  },
];

// 高级查询数据
export const superQuerySchema = {
  clientName: {title: '客户端名称',order: 0,view: 'text', type: 'string',},
  apiKey: {title: 'API密钥',order: 1,view: 'text', type: 'string',},
  status: {title: '状态：1-启用，0-禁用',order: 3,view: 'number', type: 'number',},
  expireTime: {title: '过期时间',order: 4,view: 'datetime', type: 'string',},
  ipWhitelist: {title: 'IP白名单，多个IP用逗号分隔',order: 5,view: 'textarea', type: 'string',},
  remark: {title: '备注',order: 6,view: 'textarea', type: 'string',},
};
