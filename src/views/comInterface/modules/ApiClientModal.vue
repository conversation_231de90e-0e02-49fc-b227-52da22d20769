<template>
  <a-spin :spinning="confirmLoading">
    <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <a-row>
        <a-col :span="24">
          <a-form-item label="客户端名称" v-bind="validateInfos.clientName">
            <a-input v-model:value="formData.clientName" placeholder="请输入客户端名称" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row v-if="!formData.id">
        <a-col :span="24">
          <a-alert message="API密钥和秘钥将在保存后自动生成" type="info" show-icon style="margin-bottom: 16px"/>
        </a-col>
      </a-row>

      <a-row v-if="formData.id">
        <a-col :span="24">
          <a-form-item label="API密钥">
            <a-input v-model:value="formData.apiKey" :disabled="true">
              <template #suffix>
                <CopyOutlined @click="copyToClipboard(formData.apiKey)" style="cursor: pointer"/>
              </template>
            </a-input>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row v-if="formData.id">
        <a-col :span="24">
          <a-form-item label="API秘钥">
            <a-input-password v-model:value="formData.apiSecret" :disabled="true">
              <template #suffix>
                <CopyOutlined @click="copyToClipboard(formData.apiSecret)" style="cursor: pointer"/>
              </template>
            </a-input-password>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="12">
          <a-form-item label="状态" v-bind="validateInfos.status">
            <j-dict-select-tag v-model:value="formData.status" dictCode="api_client_status" placeholder="请选择状态" :disabled="disabled"/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="过期时间">
            <j-date picker="datetime" v-model:value="formData.expireTime" placeholder="请选择过期时间" style="width: 100%" :disabled="disabled"/>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="24">
          <a-form-item label="IP白名单">
            <a-textarea v-model:value="formData.ipWhitelist" placeholder="多个IP用逗号分隔，如：***********,***********" :rows="3" :disabled="disabled"/>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="24">
          <a-form-item label="备注">
            <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息" :rows="3" :disabled="disabled"/>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed } from 'vue';
  import { CopyOutlined } from '@ant-design/icons-vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JDate from '/@/components/Form/src/jeecg/components/JDate.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../ApiClient.api';
  import { Form } from 'ant-design-vue';

  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => {} },
    formBpm: { type: Boolean, default: true },
  });

  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    clientName: '',
    apiKey: '',
    apiSecret: '',
    status: 1,
    expireTime: undefined,
    ipWhitelist: '',
    remark: '',
  });

  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);

  //表单验证
  const validatorRules = reactive({
    clientName: [{ required: true, message: '请输入客户端名称!' }],
    status: [{ required: true, message: '请选择状态!' }],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  /**
   * 新增
   */
  function add(record) {
    edit(record);
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      //赋值
      Object.assign(formData, record);
      // 设置默认状态为启用
      if (!record.id) {
        formData.status = 1;
      }
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    await validate();
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  /**
   * 复制到剪贴板
   */
  function copyToClipboard(text) {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(text).then(() => {
        createMessage.success('已复制到剪贴板');
      }).catch(() => {
        fallbackCopyTextToClipboard(text);
      });
    } else {
      fallbackCopyTextToClipboard(text);
    }
  }

  /**
   * 降级复制方法
   */
  function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
      document.execCommand('copy');
      createMessage.success('已复制到剪贴板');
    } catch (err) {
      createMessage.error('复制失败，请手动复制');
    }
    document.body.removeChild(textArea);
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    height: 500px !important;
    overflow-y: auto;
    padding: 14px;
  }
</style>
