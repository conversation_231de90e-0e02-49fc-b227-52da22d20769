# API Key管理功能说明文档

## 功能概述

本系统提供了完整的API Key管理功能，包括API密钥的生成、验证、管理和使用。支持多种密钥生成算法和强度验证，确保API接口的安全性。

## 主要功能

### 1. API客户端管理
- **客户端配置**: 管理API客户端的基本信息，包括客户端名称、状态、过期时间等
- **IP白名单**: 支持IP白名单限制，提高安全性
- **状态管理**: 支持启用/禁用API客户端

### 2. 密钥生成功能

#### 2.1 一键生成密钥
- **功能**: 快速生成高强度的API密钥对
- **算法**: 使用SecureRandom生成安全随机字符串
- **特点**: 
  - API密钥：32位字母数字组合
  - API秘钥：64位字母数字组合
  - 自动验证密钥强度

#### 2.2 指定类型生成
支持以下几种密钥类型：

1. **标准密钥 (STANDARD)**
   - API密钥：32位字母数字
   - API秘钥：64位字母数字
   - 适用于一般安全要求

2. **高强度密钥 (SECURE)**
   - API密钥：32位字母数字+特殊字符
   - API秘钥：64位字母数字+特殊字符
   - 适用于高安全要求

3. **十六进制密钥 (HEX)**
   - API密钥：32位十六进制字符
   - API秘钥：64位十六进制字符
   - 适用于特定系统集成

4. **Base64密钥 (BASE64)**
   - API密钥：Base64编码格式
   - API秘钥：Base64编码格式
   - 适用于需要Base64格式的场景

#### 2.3 自定义密钥生成
- **长度自定义**: 支持16-128位API密钥，32-256位API秘钥
- **字符类型选择**: 
  - 大写字母 (A-Z)
  - 小写字母 (a-z)
  - 数字 (0-9)
  - 特殊字符 (!@#$%^&*)
- **实时预览**: 生成前可预览密钥效果
- **强度评估**: 实时显示密钥强度等级

### 3. 密钥强度验证

#### 3.1 强度等级
- **弱 (1级)**: 基础安全要求
- **中 (2级)**: 中等安全要求
- **强 (3级)**: 高安全要求

#### 3.2 评估标准
- **长度检查**: 密钥长度是否符合要求
- **字符复杂度**: 是否包含多种字符类型
- **随机性检查**: 是否存在重复模式
- **综合评分**: 基于多个维度的综合评估

### 4. API认证机制

#### 4.1 签名算法
使用MD5签名算法：`MD5(apiKey + apiSecret + timestamp + nonce)`

#### 4.2 请求头参数
- `X-API-Key`: API密钥
- `X-Timestamp`: 时间戳（5分钟内有效）
- `X-Nonce`: 随机数
- `X-Signature`: 签名字符串

#### 4.3 安全特性
- **时间戳验证**: 防止重放攻击
- **IP白名单**: 限制访问来源
- **状态检查**: 验证客户端是否启用
- **过期时间**: 支持密钥过期管理

## 使用说明

### 1. 创建API客户端
1. 进入API客户端管理页面
2. 点击"新增"按钮
3. 填写客户端名称等基本信息
4. 系统自动生成API密钥对
5. 保存配置

### 2. 生成新密钥
1. 在编辑页面点击"一键生成"按钮
2. 或选择"更多选项"选择特定类型
3. 系统生成新的密钥对
4. 自动验证密钥强度

### 3. 自定义密钥生成
1. 选择"自定义密钥"选项
2. 设置密钥长度和字符类型
3. 预览生成效果
4. 确认生成并应用

### 4. 密钥强度验证
1. 输入API密钥和秘钥
2. 点击"验证强度"按钮
3. 查看强度等级和建议

## API接口说明

### 1. 密钥生成接口

#### 一键生成密钥
```
POST /comInterface/apiClient/generateKeys
```

#### 指定类型生成
```
POST /comInterface/apiClient/generateKeysByType
参数: type (STANDARD|SECURE|HEX|BASE64)
```

#### 自定义生成
```
POST /comInterface/apiClient/generateCustomKeys
参数: keyLength, secretLength, includeSpecialChars
```

### 2. 密钥验证接口

#### 强度验证
```
POST /comInterface/apiClient/validateKeyStrength
参数: apiKey, apiSecret
```

### 3. 客户端管理接口

#### 重新生成密钥
```
POST /comInterface/apiClient/regenerateKeys
参数: id
```

#### 启用/禁用
```
POST /comInterface/apiClient/toggleStatus
参数: id, status
```

## 安全建议

### 1. 密钥管理
- 定期更换API密钥
- 使用高强度密钥类型
- 妥善保管密钥信息
- 避免在代码中硬编码密钥

### 2. 访问控制
- 配置IP白名单
- 设置合理的过期时间
- 及时禁用不需要的客户端
- 监控API调用情况

### 3. 传输安全
- 使用HTTPS传输
- 验证服务器证书
- 实现请求签名验证
- 处理时间戳过期

## 技术实现

### 1. 后端实现
- **ApiClientController**: API客户端管理控制器
- **ApiClientService**: 密钥生成和验证服务
- **ApiKeyGenerator**: 密钥生成工具类
- **ApiAuthInterceptor**: API认证拦截器

### 2. 前端实现
- **ApiClientList**: 客户端列表页面
- **ApiClientForm**: 客户端编辑表单
- **CustomKeyGeneratorModal**: 自定义密钥生成器

### 3. 数据库设计
- **api_client**: API客户端配置表
  - id: 主键ID
  - client_name: 客户端名称
  - api_key: API密钥
  - api_secret: API秘钥
  - status: 状态
  - expire_time: 过期时间
  - ip_whitelist: IP白名单
  - remark: 备注

## 更新日志

### v1.0 (2024-07-31)
- 实现基础API密钥管理功能
- 添加一键生成密钥功能
- 支持多种密钥生成类型
- 实现密钥强度验证
- 添加自定义密钥生成器
- 完善API认证机制

## 联系支持

如有问题或建议，请联系开发团队。
